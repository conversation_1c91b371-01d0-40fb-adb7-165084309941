/* 基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 14px;
    line-height: 1.4;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    width: 380px;
    min-height: 500px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    overflow: hidden;
}

/* 头部样式 */
.header {
    background: linear-gradient(135deg, #ff9a56 0%, #ff6b6b 100%);
    color: white;
    padding: 20px;
    text-align: center;
    position: relative;
}

.logo {
    font-size: 32px;
    margin-bottom: 8px;
    display: inline-block;
}

.header h1 {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
}

/* 状态区域 */
.status {
    padding: 20px;
    text-align: center;
    min-height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.loading {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #666;
}

.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #ff6b6b;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.message {
    color: #666;
    font-size: 14px;
}

/* 按钮样式 */
.buttons {
    padding: 0 20px 20px;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.btn {
    padding: 12px 16px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.btn-primary {
    background: linear-gradient(135deg, #ff6b6b 0%, #ff9a56 100%);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 107, 107, 0.4);
}

.btn-secondary {
    background: #f8f9fa;
    color: #666;
    border: 1px solid #e9ecef;
}

.btn-secondary:hover:not(:disabled) {
    background: #e9ecef;
    color: #333;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-icon {
    font-size: 16px;
}

/* 产品信息样式 */
.product-info {
    padding: 20px;
    border-top: 1px solid #e9ecef;
    background: #f8f9fa;
}

.product-info h3 {
    margin-bottom: 15px;
    color: #333;
    font-size: 16px;
    font-weight: 600;
}

.info-grid {
    display: grid;
    gap: 12px;
}

.info-item {
    display: grid;
    grid-template-columns: 80px 1fr;
    gap: 10px;
    align-items: start;
}

.info-item label {
    font-weight: 500;
    color: #666;
    font-size: 12px;
}

.info-item span {
    font-size: 13px;
    color: #333;
    word-break: break-word;
}

.price {
    font-weight: 600;
    color: #ff6b6b;
    font-size: 14px !important;
}

.rating {
    color: #ffa500;
    font-weight: 500;
}

/* 产品图片 */
.product-image {
    margin-top: 15px;
    text-align: center;
}

.product-image label {
    display: block;
    margin-bottom: 10px;
    font-weight: 500;
    color: #666;
    font-size: 12px;
}

.product-img {
    max-width: 100%;
    max-height: 150px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 设置区域 */
.settings {
    padding: 20px;
    border-top: 1px solid #e9ecef;
}

.settings details {
    cursor: pointer;
}

.settings summary {
    font-weight: 500;
    color: #666;
    padding: 8px 0;
    list-style: none;
    position: relative;
}

.settings summary::-webkit-details-marker {
    display: none;
}

.settings summary::after {
    content: '▼';
    position: absolute;
    right: 0;
    transition: transform 0.3s ease;
    font-size: 12px;
}

.settings details[open] summary::after {
    transform: rotate(180deg);
}

.settings-content {
    padding: 15px 0 5px;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.settings-content label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
    color: #666;
    cursor: pointer;
}

.settings-content input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: #ff6b6b;
}

/* 底部 */
.footer {
    padding: 15px 20px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.version {
    font-size: 12px;
    color: #999;
}

.links {
    display: flex;
    gap: 15px;
}

.links a {
    font-size: 12px;
    color: #666;
    text-decoration: none;
    transition: color 0.3s ease;
}

.links a:hover {
    color: #ff6b6b;
}

/* 响应式 */
@media (max-width: 400px) {
    .container {
        width: 100%;
        min-height: 100vh;
        border-radius: 0;
    }
    
    .info-item {
        grid-template-columns: 1fr;
        gap: 5px;
    }
    
    .info-item label {
        font-weight: 600;
    }
}

/* 动画效果 */
.product-info {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 成功/错误状态 */
.status.success {
    color: #28a745;
}

.status.error {
    color: #dc3545;
}

/* 滚动条样式 */
.container::-webkit-scrollbar {
    width: 6px;
}

.container::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
