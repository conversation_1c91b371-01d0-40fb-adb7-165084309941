<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Amazon Product Extractor - 安装指南</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #ff9a56 0%, #ff6b6b 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 600;
        }
        
        .header p {
            margin: 10px 0 0;
            opacity: 0.9;
        }
        
        .content {
            padding: 30px;
        }
        
        .step {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            background: #f8f9fa;
        }
        
        .step h3 {
            color: #ff6b6b;
            margin-top: 0;
            font-size: 18px;
        }
        
        .step-number {
            display: inline-block;
            width: 30px;
            height: 30px;
            background: #ff6b6b;
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 30px;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .code {
            background: #f1f3f4;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
        }
        
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .button {
            display: inline-block;
            padding: 12px 24px;
            background: linear-gradient(135deg, #ff6b6b 0%, #ff9a56 100%);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 500;
            transition: transform 0.3s ease;
        }
        
        .button:hover {
            transform: translateY(-2px);
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .feature {
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            text-align: center;
        }
        
        .feature-icon {
            font-size: 48px;
            margin-bottom: 15px;
        }
        
        .feature h4 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .feature p {
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📦 Amazon Product Extractor</h1>
            <p>Chrome浏览器插件 - 快速安装指南</p>
        </div>
        
        <div class="content">
            <div class="success">
                <strong>🎉 恭喜！</strong> 您已经成功下载了Amazon Product Extractor插件文件。
            </div>
            
            <h2>🚀 快速安装步骤</h2>
            
            <div class="step">
                <h3><span class="step-number">1</span>打开Chrome扩展程序页面</h3>
                <p>在Chrome浏览器地址栏中输入以下地址：</p>
                <div class="code">chrome://extensions/</div>
                <p>或者通过菜单：Chrome菜单 → 更多工具 → 扩展程序</p>
            </div>
            
            <div class="step">
                <h3><span class="step-number">2</span>启用开发者模式</h3>
                <p>在扩展程序页面的右上角，找到"开发者模式"开关并启用它。</p>
                <div class="warning">
                    <strong>注意：</strong> 开发者模式是安装未打包扩展程序所必需的。
                </div>
            </div>
            
            <div class="step">
                <h3><span class="step-number">3</span>加载插件</h3>
                <p>点击"加载已解压的扩展程序"按钮，然后选择包含插件文件的文件夹：</p>
                <div class="code">AmazonProductExtractor</div>
                <p>确保选择的是包含 manifest.json 文件的文件夹。</p>
            </div>
            
            <div class="step">
                <h3><span class="step-number">4</span>验证安装</h3>
                <p>安装成功后，您应该能在扩展程序列表中看到"Amazon Product Extractor"。</p>
                <p>同时，浏览器工具栏中会出现插件图标。</p>
            </div>
            
            <div class="step">
                <h3><span class="step-number">5</span>开始使用</h3>
                <p>访问任意Amazon产品页面，点击插件图标即可开始提取产品信息！</p>
                <a href="https://www.amazon.com" class="button" target="_blank">前往Amazon测试</a>
            </div>
            
            <h2>✨ 主要功能</h2>
            
            <div class="features">
                <div class="feature">
                    <div class="feature-icon">🔍</div>
                    <h4>智能提取</h4>
                    <p>自动识别并提取产品的详细信息，包括价格、评分、品牌等</p>
                </div>
                
                <div class="feature">
                    <div class="feature-icon">📊</div>
                    <h4>多格式导出</h4>
                    <p>支持导出为Excel(CSV)和JSON格式，满足不同需求</p>
                </div>
                
                <div class="feature">
                    <div class="feature-icon">🌍</div>
                    <h4>多站点支持</h4>
                    <p>支持全球多个Amazon站点，包括美国、英国、德国等</p>
                </div>
                
                <div class="feature">
                    <div class="feature-icon">💾</div>
                    <h4>历史记录</h4>
                    <p>自动保存提取历史，方便回顾和管理</p>
                </div>
            </div>
            
            <h2>🛠️ 故障排除</h2>
            
            <div class="step">
                <h3>常见问题解决方案</h3>
                <ul>
                    <li><strong>插件无法加载：</strong> 确保选择了正确的文件夹，且包含manifest.json文件</li>
                    <li><strong>无法提取数据：</strong> 确保在Amazon产品详情页面使用插件</li>
                    <li><strong>导出功能异常：</strong> 检查浏览器的下载权限设置</li>
                </ul>
            </div>
            
            <div class="warning">
                <strong>重要提醒：</strong>
                <ul>
                    <li>本插件仅用于学习和研究目的</li>
                    <li>请遵守Amazon的使用条款</li>
                    <li>避免过度频繁使用，以免对服务器造成负担</li>
                </ul>
            </div>
            
            <div class="success">
                <strong>🎯 安装完成！</strong> 现在您可以开始使用Amazon Product Extractor来提取产品信息了。
                <br><br>
                如有任何问题，请查看README.md文件获取更详细的说明。
            </div>
        </div>
    </div>
</body>
</html>
