<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Amazon Product Extractor</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">📦</div>
            <h1>Amazon Product Extractor</h1>
        </div>
        
        <div class="status" id="status">
            <div class="loading" id="loading" style="display: none;">
                <div class="spinner"></div>
                <span>正在提取产品信息...</span>
            </div>
            <div class="message" id="message">点击"提取产品信息"开始</div>
        </div>
        
        <div class="buttons">
            <button id="extractBtn" class="btn btn-primary">
                <span class="btn-icon">📦</span>
                提取产品信息
            </button>
            <button id="exportExcelBtn" class="btn btn-secondary" disabled>
                <span class="btn-icon">📊</span>
                导出Excel
            </button>
            <button id="exportJsonBtn" class="btn btn-secondary" disabled>
                <span class="btn-icon">📄</span>
                导出JSON
            </button>
        </div>
        
        <div class="product-info" id="productInfo" style="display: none;">
            <h3>产品信息</h3>
            <div class="info-grid">
                <div class="info-item">
                    <label>产品名称:</label>
                    <span id="productTitle">-</span>
                </div>
                <div class="info-item">
                    <label>ASIN码:</label>
                    <span id="productAsin">-</span>
                </div>
                <div class="info-item">
                    <label>当前价格:</label>
                    <span id="productPrice" class="price">-</span>
                </div>
                <div class="info-item">
                    <label>原价:</label>
                    <span id="productOriginalPrice">-</span>
                </div>
                <div class="info-item">
                    <label>评星:</label>
                    <span id="productRating" class="rating">-</span>
                </div>
                <div class="info-item">
                    <label>评论数:</label>
                    <span id="productReviews">-</span>
                </div>
                <div class="info-item">
                    <label>品牌:</label>
                    <span id="productBrand">-</span>
                </div>
                <div class="info-item">
                    <label>可用性:</label>
                    <span id="productAvailability">-</span>
                </div>
            </div>
            
            <div class="product-image" id="productImageContainer" style="display: none;">
                <label>产品图片:</label>
                <img id="productImage" alt="产品图片" class="product-img">
            </div>
        </div>
        
        <div class="settings">
            <details>
                <summary>设置选项</summary>
                <div class="settings-content">
                    <label>
                        <input type="checkbox" id="autoExtract" checked>
                        自动检测Amazon产品页面
                    </label>
                    <label>
                        <input type="checkbox" id="includeImage" checked>
                        包含产品图片
                    </label>
                    <label>
                        <input type="checkbox" id="saveHistory" checked>
                        保存提取历史
                    </label>
                </div>
            </details>
        </div>
        
        <div class="footer">
            <div class="version">v1.0</div>
            <div class="links">
                <a href="#" id="helpBtn">帮助</a>
                <a href="#" id="historyBtn">历史记录</a>
            </div>
        </div>
    </div>
    
    <script src="popup.js"></script>
</body>
</html>
