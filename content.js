// content.js - 内容脚本，用于从Amazon页面提取产品信息

class AmazonProductExtractor {
    constructor() {
        this.selectors = {
            // 产品标题
            title: [
                '#productTitle',
                '.product-title',
                'h1[data-automation-id="product-title"]',
                'h1.a-size-large'
            ],
            
            // 产品图片
            image: [
                '#landingImage',
                '#imgBlkFront',
                '.a-dynamic-image',
                'img[data-old-hires]',
                '.image-wrapper img'
            ],
            
            // 当前价格
            currentPrice: [
                '.a-price-current .a-offscreen',
                '.a-price .a-offscreen',
                '#priceblock_dealprice',
                '#priceblock_ourprice',
                '.a-price-range .a-offscreen',
                '.a-price-whole'
            ],
            
            // 原价
            originalPrice: [
                '.a-price-was .a-offscreen',
                '#priceblock_listprice',
                '.a-text-strike .a-offscreen'
            ],
            
            // 评星
            rating: [
                '.a-icon-alt',
                '[data-hook="average-star-rating"] .a-icon-alt',
                '.reviewCountTextLinkedHistogram .a-icon-alt'
            ],
            
            // 评论数
            reviewCount: [
                '#acrCustomerReviewText',
                '[data-hook="total-review-count"]',
                '.reviewCountTextLinkedHistogram .a-size-base'
            ],
            
            // 品牌
            brand: [
                '#bylineInfo',
                '.author .a-link-normal',
                '[data-automation-id="product-brand"]'
            ],
            
            // 可用性
            availability: [
                '#availability span',
                '.a-color-success',
                '.a-color-state',
                '#merchant-info'
            ]
        };
        
        this.init();
    }

    init() {
        // 监听来自popup的消息
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            if (request.action === 'extractProductInfo') {
                this.extractProductInfo()
                    .then(data => sendResponse({ success: true, data }))
                    .catch(error => sendResponse({ success: false, error: error.message }));
                return true; // 保持消息通道开放
            }
        });
    }

    async extractProductInfo() {
        try {
            // 检查是否在Amazon产品页面
            if (!this.isAmazonProductPage()) {
                throw new Error('当前页面不是Amazon产品页面');
            }

            const productData = {
                url: window.location.href,
                extractedAt: new Date().toISOString(),
                title: this.extractText(this.selectors.title),
                imageUrl: this.extractImageUrl(this.selectors.image),
                currentPrice: this.extractPrice(this.selectors.currentPrice),
                originalPrice: this.extractPrice(this.selectors.originalPrice),
                rating: this.extractRating(this.selectors.rating),
                reviewCount: this.extractReviewCount(this.selectors.reviewCount),
                brand: this.extractText(this.selectors.brand),
                availability: this.extractText(this.selectors.availability),
                asin: this.extractASIN()
            };

            // 清理数据
            this.cleanProductData(productData);
            
            console.log('提取的产品数据:', productData);
            return productData;
            
        } catch (error) {
            console.error('提取产品信息失败:', error);
            throw error;
        }
    }

    isAmazonProductPage() {
        const hostname = window.location.hostname;
        const pathname = window.location.pathname;
        
        // 检查是否是Amazon域名
        const isAmazonDomain = hostname.includes('amazon.');
        
        // 检查是否是产品页面（通常包含/dp/或/gp/product/）
        const isProductPage = pathname.includes('/dp/') || 
                             pathname.includes('/gp/product/') ||
                             document.querySelector('#productTitle') !== null;
        
        return isAmazonDomain && isProductPage;
    }

    extractText(selectors) {
        for (const selector of selectors) {
            const element = document.querySelector(selector);
            if (element) {
                return element.textContent.trim();
            }
        }
        return '';
    }

    extractImageUrl(selectors) {
        for (const selector of selectors) {
            const element = document.querySelector(selector);
            if (element) {
                // 尝试获取高分辨率图片
                const hiresUrl = element.getAttribute('data-old-hires') || 
                               element.getAttribute('data-a-dynamic-image') ||
                               element.src;
                
                if (hiresUrl) {
                    // 如果是data-a-dynamic-image，解析JSON获取最大尺寸图片
                    if (element.getAttribute('data-a-dynamic-image')) {
                        try {
                            const imageData = JSON.parse(element.getAttribute('data-a-dynamic-image'));
                            const urls = Object.keys(imageData);
                            if (urls.length > 0) {
                                return urls[0]; // 返回第一个（通常是最大的）
                            }
                        } catch (e) {
                            // 如果解析失败，使用src
                        }
                    }
                    return hiresUrl;
                }
            }
        }
        return '';
    }

    extractPrice(selectors) {
        for (const selector of selectors) {
            const element = document.querySelector(selector);
            if (element) {
                const text = element.textContent.trim();
                // 提取价格数字
                const priceMatch = text.match(/[\d,]+\.?\d*/);
                if (priceMatch) {
                    return text;
                }
            }
        }
        return '';
    }

    extractRating(selectors) {
        for (const selector of selectors) {
            const element = document.querySelector(selector);
            if (element) {
                const text = element.textContent || element.getAttribute('alt') || '';
                // 提取评星数字
                const ratingMatch = text.match(/(\d+\.?\d*)\s*out\s*of\s*5|(\d+\.?\d*)\s*星|(\d+\.?\d*)/);
                if (ratingMatch) {
                    return ratingMatch[1] || ratingMatch[2] || ratingMatch[3];
                }
            }
        }
        return '';
    }

    extractReviewCount(selectors) {
        for (const selector of selectors) {
            const element = document.querySelector(selector);
            if (element) {
                const text = element.textContent.trim();
                // 提取评论数字
                const countMatch = text.match(/([\d,]+)/);
                if (countMatch) {
                    return countMatch[1];
                }
            }
        }
        return '';
    }

    extractASIN() {
        // 从URL中提取ASIN
        const urlMatch = window.location.pathname.match(/\/dp\/([A-Z0-9]{10})/);
        if (urlMatch) {
            return urlMatch[1];
        }
        
        // 从页面元素中提取ASIN
        const asinElement = document.querySelector('[data-asin]');
        if (asinElement) {
            return asinElement.getAttribute('data-asin');
        }
        
        // 从隐藏输入中提取
        const hiddenAsin = document.querySelector('input[name="ASIN"]');
        if (hiddenAsin) {
            return hiddenAsin.value;
        }
        
        return '';
    }

    cleanProductData(data) {
        // 清理标题
        if (data.title) {
            data.title = data.title.replace(/\s+/g, ' ').trim();
        }
        
        // 清理价格
        if (data.currentPrice) {
            data.currentPrice = data.currentPrice.replace(/[^\d.,]/g, '').trim();
        }
        if (data.originalPrice) {
            data.originalPrice = data.originalPrice.replace(/[^\d.,]/g, '').trim();
        }
        
        // 清理评星
        if (data.rating) {
            const ratingNum = parseFloat(data.rating);
            if (!isNaN(ratingNum)) {
                data.rating = ratingNum.toString();
            }
        }
        
        // 清理评论数
        if (data.reviewCount) {
            data.reviewCount = data.reviewCount.replace(/[^\d,]/g, '');
        }
        
        // 清理品牌
        if (data.brand) {
            data.brand = data.brand.replace(/^(Brand:\s*|Visit the\s*|品牌:\s*)/i, '').trim();
        }
        
        // 清理可用性
        if (data.availability) {
            data.availability = data.availability.replace(/\s+/g, ' ').trim();
        }
    }
}

// 初始化提取器
new AmazonProductExtractor();
