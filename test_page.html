<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Amazon Product Extractor - 测试页面</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #ff9a56 0%, #ff6b6b 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .content {
            padding: 30px;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
        }
        
        .test-section h3 {
            color: #333;
            margin-top: 0;
        }
        
        .test-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .test-link {
            display: block;
            padding: 15px;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            text-decoration: none;
            color: #333;
            transition: all 0.3s ease;
        }
        
        .test-link:hover {
            background: #e9ecef;
            transform: translateY(-2px);
        }
        
        .test-link strong {
            color: #ff6b6b;
            display: block;
            margin-bottom: 5px;
        }
        
        .test-link small {
            color: #666;
            font-size: 12px;
        }
        
        .instructions {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .instructions h4 {
            color: #1976d2;
            margin-top: 0;
        }
        
        .step {
            margin: 10px 0;
            padding-left: 20px;
            position: relative;
        }
        
        .step::before {
            content: "→";
            position: absolute;
            left: 0;
            color: #ff6b6b;
            font-weight: bold;
        }
        
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 Amazon Product Extractor 测试页面</h1>
            <p>使用以下链接测试插件功能</p>
        </div>
        
        <div class="content">
            <div class="instructions">
                <h4>📋 测试步骤</h4>
                <div class="step">确保已正确安装Amazon Product Extractor插件</div>
                <div class="step">点击下方任意测试链接打开Amazon产品页面</div>
                <div class="step">在产品页面点击浏览器工具栏中的插件图标</div>
                <div class="step">点击"提取产品信息"按钮</div>
                <div class="step">查看提取结果并测试导出功能</div>
            </div>
            
            <div class="test-section">
                <h3>🇺🇸 Amazon美国站测试链接</h3>
                <div class="test-links">
                    <a href="https://www.amazon.com/dp/B08N5WRWNW" class="test-link" target="_blank">
                        <strong>Echo Dot (4th Gen)</strong>
                        <div>智能音箱产品页面</div>
                        <small>测试基本产品信息提取</small>
                    </a>
                    
                    <a href="https://www.amazon.com/dp/B0BDJQ9X8F" class="test-link" target="_blank">
                        <strong>iPhone 14 Pro Max</strong>
                        <div>高价值电子产品</div>
                        <small>测试价格和评论提取</small>
                    </a>
                    
                    <a href="https://www.amazon.com/dp/B08GGGMN2P" class="test-link" target="_blank">
                        <strong>Fire TV Stick 4K Max</strong>
                        <div>流媒体设备</div>
                        <small>测试图片和品牌信息</small>
                    </a>
                    
                    <a href="https://www.amazon.com/dp/B07FZ8S74R" class="test-link" target="_blank">
                        <strong>Echo Show 8</strong>
                        <div>带屏幕的智能设备</div>
                        <small>测试复杂页面结构</small>
                    </a>
                </div>
            </div>
            
            <div class="test-section">
                <h3>🇬🇧 Amazon英国站测试链接</h3>
                <div class="test-links">
                    <a href="https://www.amazon.co.uk/dp/B08GGGMN2P" class="test-link" target="_blank">
                        <strong>Fire TV Stick 4K Max (UK)</strong>
                        <div>英国站点产品</div>
                        <small>测试多站点支持</small>
                    </a>
                    
                    <a href="https://www.amazon.co.uk/dp/B08N5WRWNW" class="test-link" target="_blank">
                        <strong>Echo Dot (UK)</strong>
                        <div>英国站点智能音箱</div>
                        <small>测试英镑价格提取</small>
                    </a>
                </div>
            </div>
            
            <div class="test-section">
                <h3>🇩🇪 Amazon德国站测试链接</h3>
                <div class="test-links">
                    <a href="https://www.amazon.de/dp/B08N5WRWNW" class="test-link" target="_blank">
                        <strong>Echo Dot (DE)</strong>
                        <div>德国站点产品</div>
                        <small>测试欧元价格和德语页面</small>
                    </a>
                </div>
            </div>
            
            <div class="test-section">
                <h3>🧪 功能测试清单</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
                    <div style="padding: 15px; background: #f8f9fa; border-radius: 8px;">
                        <h4 style="margin-top: 0; color: #ff6b6b;">基础信息提取</h4>
                        <ul style="margin: 0; padding-left: 20px;">
                            <li>产品标题</li>
                            <li>ASIN码</li>
                            <li>产品链接</li>
                            <li>提取时间</li>
                        </ul>
                    </div>
                    
                    <div style="padding: 15px; background: #f8f9fa; border-radius: 8px;">
                        <h4 style="margin-top: 0; color: #ff6b6b;">价格信息</h4>
                        <ul style="margin: 0; padding-left: 20px;">
                            <li>当前价格</li>
                            <li>原价（如有折扣）</li>
                            <li>价格格式化</li>
                            <li>货币符号</li>
                        </ul>
                    </div>
                    
                    <div style="padding: 15px; background: #f8f9fa; border-radius: 8px;">
                        <h4 style="margin-top: 0; color: #ff6b6b;">评价信息</h4>
                        <ul style="margin: 0; padding-left: 20px;">
                            <li>评星等级</li>
                            <li>评论总数</li>
                            <li>评分格式</li>
                            <li>数字清理</li>
                        </ul>
                    </div>
                    
                    <div style="padding: 15px; background: #f8f9fa; border-radius: 8px;">
                        <h4 style="margin-top: 0; color: #ff6b6b;">其他信息</h4>
                        <ul style="margin: 0; padding-left: 20px;">
                            <li>品牌名称</li>
                            <li>库存状态</li>
                            <li>产品图片</li>
                            <li>图片质量</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="test-section">
                <h3>📤 导出功能测试</h3>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div>
                        <h4>Excel导出测试</h4>
                        <ul>
                            <li>点击"导出Excel"按钮</li>
                            <li>检查CSV文件是否正确下载</li>
                            <li>验证文件内容格式</li>
                            <li>确认中文字符显示正常</li>
                        </ul>
                    </div>
                    
                    <div>
                        <h4>JSON导出测试</h4>
                        <ul>
                            <li>点击"导出JSON"按钮</li>
                            <li>检查JSON文件是否正确下载</li>
                            <li>验证JSON格式是否有效</li>
                            <li>确认数据结构完整</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="warning">
                <strong>⚠️ 注意事项：</strong>
                <ul style="margin: 10px 0 0; padding-left: 20px;">
                    <li>某些产品页面可能因为地区限制无法访问</li>
                    <li>页面结构变化可能影响数据提取准确性</li>
                    <li>请适度使用，避免频繁请求</li>
                    <li>如遇到问题，请检查浏览器控制台的错误信息</li>
                </ul>
            </div>
            
            <div class="success">
                <strong>✅ 测试完成后：</strong>
                <br>如果所有功能都正常工作，说明插件安装成功！您现在可以在任何Amazon产品页面使用这个插件来提取产品信息了。
            </div>
        </div>
    </div>
</body>
</html>
