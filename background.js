// background.js - 后台脚本，处理插件的后台逻辑

class BackgroundManager {
    constructor() {
        this.init();
    }

    init() {
        // 监听插件安装事件
        chrome.runtime.onInstalled.addListener((details) => {
            this.handleInstalled(details);
        });

        // 监听标签页更新事件
        chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
            this.handleTabUpdated(tabId, changeInfo, tab);
        });

        // 监听来自content script或popup的消息
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            this.handleMessage(request, sender, sendResponse);
            return true; // 保持消息通道开放
        });

        // 监听存储变化
        chrome.storage.onChanged.addListener((changes, namespace) => {
            this.handleStorageChanged(changes, namespace);
        });
    }

    handleInstalled(details) {
        console.log('Amazon Product Extractor 已安装/更新:', details);
        
        if (details.reason === 'install') {
            // 首次安装时的初始化
            this.initializeExtension();
        } else if (details.reason === 'update') {
            // 更新时的处理
            this.handleUpdate(details.previousVersion);
        }
    }

    async initializeExtension() {
        try {
            // 设置默认配置
            const defaultSettings = {
                autoExtract: true,
                includeImage: true,
                saveHistory: true,
                maxHistoryItems: 50,
                extractDelay: 1000
            };

            await chrome.storage.sync.set(defaultSettings);
            
            // 初始化历史记录
            await chrome.storage.local.set({
                extractHistory: [],
                statistics: {
                    totalExtractions: 0,
                    lastExtractedAt: null,
                    favoriteProducts: []
                }
            });

            console.log('插件初始化完成');
        } catch (error) {
            console.error('插件初始化失败:', error);
        }
    }

    async handleUpdate(previousVersion) {
        console.log(`插件从版本 ${previousVersion} 更新到当前版本`);
        
        try {
            // 检查是否需要迁移数据
            await this.migrateDataIfNeeded(previousVersion);
        } catch (error) {
            console.error('数据迁移失败:', error);
        }
    }

    async migrateDataIfNeeded(previousVersion) {
        // 这里可以添加版本迁移逻辑
        // 例如：如果从1.0升级到1.1，可能需要更新数据结构
        console.log('检查数据迁移需求...');
    }

    handleTabUpdated(tabId, changeInfo, tab) {
        // 当标签页完成加载且是Amazon页面时
        if (changeInfo.status === 'complete' && tab.url && this.isAmazonUrl(tab.url)) {
            this.handleAmazonPageLoaded(tabId, tab);
        }
    }

    async handleAmazonPageLoaded(tabId, tab) {
        try {
            // 检查是否启用了自动提取
            const settings = await chrome.storage.sync.get('autoExtract');
            
            if (settings.autoExtract && this.isProductPage(tab.url)) {
                // 可以在这里添加自动提取逻辑
                console.log('检测到Amazon产品页面:', tab.url);
                
                // 更新图标状态
                this.updateIcon(tabId, 'active');
            } else {
                this.updateIcon(tabId, 'inactive');
            }
        } catch (error) {
            console.error('处理Amazon页面加载失败:', error);
        }
    }

    handleMessage(request, sender, sendResponse) {
        switch (request.action) {
            case 'getSettings':
                this.getSettings(sendResponse);
                break;
                
            case 'saveSettings':
                this.saveSettings(request.settings, sendResponse);
                break;
                
            case 'getHistory':
                this.getHistory(sendResponse);
                break;
                
            case 'clearHistory':
                this.clearHistory(sendResponse);
                break;
                
            case 'updateStatistics':
                this.updateStatistics(request.data, sendResponse);
                break;
                
            default:
                sendResponse({ success: false, error: '未知的操作' });
        }
    }

    async getSettings(sendResponse) {
        try {
            const settings = await chrome.storage.sync.get();
            sendResponse({ success: true, settings });
        } catch (error) {
            sendResponse({ success: false, error: error.message });
        }
    }

    async saveSettings(settings, sendResponse) {
        try {
            await chrome.storage.sync.set(settings);
            sendResponse({ success: true });
        } catch (error) {
            sendResponse({ success: false, error: error.message });
        }
    }

    async getHistory(sendResponse) {
        try {
            const data = await chrome.storage.local.get('extractHistory');
            sendResponse({ success: true, history: data.extractHistory || [] });
        } catch (error) {
            sendResponse({ success: false, error: error.message });
        }
    }

    async clearHistory(sendResponse) {
        try {
            await chrome.storage.local.set({ extractHistory: [] });
            sendResponse({ success: true });
        } catch (error) {
            sendResponse({ success: false, error: error.message });
        }
    }

    async updateStatistics(data, sendResponse) {
        try {
            const current = await chrome.storage.local.get('statistics');
            const stats = current.statistics || {};
            
            stats.totalExtractions = (stats.totalExtractions || 0) + 1;
            stats.lastExtractedAt = new Date().toISOString();
            
            await chrome.storage.local.set({ statistics: stats });
            sendResponse({ success: true });
        } catch (error) {
            sendResponse({ success: false, error: error.message });
        }
    }

    handleStorageChanged(changes, namespace) {
        console.log('存储变化:', changes, namespace);
        
        // 可以在这里处理设置变化的响应
        if (changes.autoExtract) {
            console.log('自动提取设置已更改:', changes.autoExtract.newValue);
        }
    }

    updateIcon(tabId, status) {
        // 根据状态更新图标
        const iconPath = status === 'active' ? 
            { '16': 'icons/icon16-active.png', '32': 'icons/icon32-active.png' } :
            { '16': 'icons/icon16.png', '32': 'icons/icon32.png' };
        
        // 由于我们移除了图标文件，这里暂时注释掉
        // chrome.action.setIcon({ tabId, path: iconPath });
        
        // 更新标题
        const title = status === 'active' ? 
            'Amazon Product Extractor - 可以提取产品信息' :
            'Amazon Product Extractor';
            
        chrome.action.setTitle({ tabId, title });
    }

    isAmazonUrl(url) {
        try {
            const hostname = new URL(url).hostname;
            return hostname.includes('amazon.');
        } catch (error) {
            return false;
        }
    }

    isProductPage(url) {
        try {
            const pathname = new URL(url).pathname;
            return pathname.includes('/dp/') || pathname.includes('/gp/product/');
        } catch (error) {
            return false;
        }
    }
}

// 初始化后台管理器
new BackgroundManager();
