// popup.js - 插件弹窗逻辑

class PopupManager {
    constructor() {
        this.currentProductData = null;
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadSettings();
        this.checkCurrentPage();
    }

    bindEvents() {
        // 提取按钮
        document.getElementById('extractBtn').addEventListener('click', () => {
            this.extractProductInfo();
        });

        // 导出按钮
        document.getElementById('exportExcelBtn').addEventListener('click', () => {
            this.exportToExcel();
        });

        document.getElementById('exportJsonBtn').addEventListener('click', () => {
            this.exportToJson();
        });

        // 设置选项
        document.getElementById('autoExtract').addEventListener('change', (e) => {
            this.saveSetting('autoExtract', e.target.checked);
        });

        document.getElementById('includeImage').addEventListener('change', (e) => {
            this.saveSetting('includeImage', e.target.checked);
        });

        document.getElementById('saveHistory').addEventListener('change', (e) => {
            this.saveSetting('saveHistory', e.target.checked);
        });

        // 帮助和历史记录
        document.getElementById('helpBtn').addEventListener('click', () => {
            this.showHelp();
        });

        document.getElementById('historyBtn').addEventListener('click', () => {
            this.showHistory();
        });
    }

    async loadSettings() {
        try {
            const settings = await chrome.storage.sync.get({
                autoExtract: true,
                includeImage: true,
                saveHistory: true
            });

            document.getElementById('autoExtract').checked = settings.autoExtract;
            document.getElementById('includeImage').checked = settings.includeImage;
            document.getElementById('saveHistory').checked = settings.saveHistory;
        } catch (error) {
            console.error('加载设置失败:', error);
        }
    }

    async saveSetting(key, value) {
        try {
            await chrome.storage.sync.set({ [key]: value });
        } catch (error) {
            console.error('保存设置失败:', error);
        }
    }

    async checkCurrentPage() {
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            
            if (tab.url && tab.url.includes('amazon.')) {
                this.updateStatus('检测到Amazon产品页面', 'success');
                
                // 如果启用了自动提取，则自动开始
                const settings = await chrome.storage.sync.get('autoExtract');
                if (settings.autoExtract) {
                    setTimeout(() => this.extractProductInfo(), 1000);
                }
            } else {
                this.updateStatus('请在Amazon产品页面使用此插件', 'error');
                document.getElementById('extractBtn').disabled = true;
            }
        } catch (error) {
            console.error('检查页面失败:', error);
            this.updateStatus('无法检测当前页面', 'error');
        }
    }

    async extractProductInfo() {
        this.showLoading(true);
        this.updateStatus('正在提取产品信息...', '');

        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            
            // 向content script发送提取请求
            const response = await chrome.tabs.sendMessage(tab.id, {
                action: 'extractProductInfo'
            });

            if (response && response.success) {
                this.currentProductData = response.data;
                this.displayProductInfo(response.data);
                this.updateStatus('产品信息提取成功！', 'success');
                
                // 启用导出按钮
                document.getElementById('exportExcelBtn').disabled = false;
                document.getElementById('exportJsonBtn').disabled = false;

                // 保存到历史记录
                const settings = await chrome.storage.sync.get('saveHistory');
                if (settings.saveHistory) {
                    this.saveToHistory(response.data);
                }
            } else {
                throw new Error(response?.error || '提取失败');
            }
        } catch (error) {
            console.error('提取产品信息失败:', error);
            this.updateStatus(`提取失败: ${error.message}`, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    displayProductInfo(data) {
        // 显示产品信息区域
        document.getElementById('productInfo').style.display = 'block';

        // 填充数据
        document.getElementById('productTitle').textContent = data.title || '-';
        document.getElementById('productAsin').textContent = data.asin || '-';
        document.getElementById('productPrice').textContent = data.currentPrice || '-';
        document.getElementById('productOriginalPrice').textContent = data.originalPrice || '-';
        document.getElementById('productRating').textContent = data.rating || '-';
        document.getElementById('productReviews').textContent = data.reviewCount || '-';
        document.getElementById('productBrand').textContent = data.brand || '-';
        document.getElementById('productAvailability').textContent = data.availability || '-';

        // 显示产品图片
        const settings = document.getElementById('includeImage').checked;
        if (settings && data.imageUrl) {
            const imageContainer = document.getElementById('productImageContainer');
            const imageElement = document.getElementById('productImage');
            
            imageElement.src = data.imageUrl;
            imageElement.onerror = () => {
                imageContainer.style.display = 'none';
            };
            imageContainer.style.display = 'block';
        } else {
            document.getElementById('productImageContainer').style.display = 'none';
        }
    }

    async exportToExcel() {
        if (!this.currentProductData) {
            this.updateStatus('没有可导出的数据', 'error');
            return;
        }

        try {
            // 创建CSV格式数据（Excel兼容）
            const headers = [
                '产品名称', '产品图片URL', '产品原价', '产品折扣价',
                '产品评星', '评论数', 'ASIN码', '产品链接', '品牌', '可用性'
            ];

            const data = this.currentProductData;
            const row = [
                data.title || '',
                data.imageUrl || '',
                data.originalPrice || '',
                data.currentPrice || '',
                data.rating || '',
                data.reviewCount || '',
                data.asin || '',
                data.url || '',
                data.brand || '',
                data.availability || ''
            ];

            // 转换为CSV格式
            const csvContent = [
                headers.join(','),
                row.map(field => `"${String(field).replace(/"/g, '""')}"`).join(',')
            ].join('\n');

            // 创建下载
            const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
            const url = URL.createObjectURL(blob);
            
            const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
            const filename = `amazon_product_${timestamp}.csv`;

            await chrome.downloads.download({
                url: url,
                filename: filename,
                saveAs: true
            });

            this.updateStatus('Excel文件导出成功！', 'success');
        } catch (error) {
            console.error('导出Excel失败:', error);
            this.updateStatus('导出Excel失败', 'error');
        }
    }

    async exportToJson() {
        if (!this.currentProductData) {
            this.updateStatus('没有可导出的数据', 'error');
            return;
        }

        try {
            const jsonContent = JSON.stringify(this.currentProductData, null, 2);
            const blob = new Blob([jsonContent], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            
            const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
            const filename = `amazon_product_${timestamp}.json`;

            await chrome.downloads.download({
                url: url,
                filename: filename,
                saveAs: true
            });

            this.updateStatus('JSON文件导出成功！', 'success');
        } catch (error) {
            console.error('导出JSON失败:', error);
            this.updateStatus('导出JSON失败', 'error');
        }
    }

    async saveToHistory(data) {
        try {
            const history = await chrome.storage.local.get('extractHistory');
            const historyList = history.extractHistory || [];
            
            // 添加时间戳
            const historyItem = {
                ...data,
                extractedAt: new Date().toISOString(),
                id: Date.now()
            };

            historyList.unshift(historyItem);
            
            // 只保留最近50条记录
            if (historyList.length > 50) {
                historyList.splice(50);
            }

            await chrome.storage.local.set({ extractHistory: historyList });
        } catch (error) {
            console.error('保存历史记录失败:', error);
        }
    }

    showLoading(show) {
        document.getElementById('loading').style.display = show ? 'flex' : 'none';
        document.getElementById('message').style.display = show ? 'none' : 'block';
        document.getElementById('extractBtn').disabled = show;
    }

    updateStatus(message, type = '') {
        const statusElement = document.getElementById('status');
        const messageElement = document.getElementById('message');
        
        messageElement.textContent = message;
        statusElement.className = `status ${type}`;
    }

    showHelp() {
        alert(`Amazon Product Extractor 使用说明：

1. 在Amazon产品页面打开插件
2. 点击"提取产品信息"按钮
3. 查看提取的产品详细信息
4. 可以导出为Excel或JSON格式

支持的Amazon站点：
- amazon.com (美国)
- amazon.cn (中国)
- amazon.co.uk (英国)
- amazon.de (德国)
- amazon.fr (法国)
- amazon.it (意大利)
- amazon.es (西班牙)
- amazon.ca (加拿大)
- amazon.com.au (澳大利亚)
- amazon.co.jp (日本)

注意事项：
- 请确保在产品详情页面使用
- 某些页面元素可能因地区而异
- 建议启用"自动检测"功能`);
    }

    async showHistory() {
        try {
            const history = await chrome.storage.local.get('extractHistory');
            const historyList = history.extractHistory || [];
            
            if (historyList.length === 0) {
                alert('暂无历史记录');
                return;
            }

            const historyText = historyList.slice(0, 10).map((item, index) => {
                const date = new Date(item.extractedAt).toLocaleString();
                return `${index + 1}. ${item.title || '未知产品'}\n   时间: ${date}\n   ASIN: ${item.asin || 'N/A'}`;
            }).join('\n\n');

            alert(`最近的提取记录：\n\n${historyText}`);
        } catch (error) {
            console.error('获取历史记录失败:', error);
            alert('获取历史记录失败');
        }
    }
}

// 初始化弹窗管理器
document.addEventListener('DOMContentLoaded', () => {
    new PopupManager();
});
