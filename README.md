# Amazon Product Extractor - Chrome浏览器插件

一个专门用于抓取亚马逊美国站产品信息的Chrome浏览器插件。

## 功能特性

- 🔍 **智能提取**: 自动识别Amazon产品页面并提取详细信息
- 📊 **多格式导出**: 支持导出为Excel(CSV)和JSON格式
- 🎯 **精准数据**: 提取产品名称、价格、评星、评论数、品牌等关键信息
- 🖼️ **图片获取**: 自动获取产品高清图片链接
- 📱 **响应式设计**: 美观的用户界面，支持多种屏幕尺寸
- 💾 **历史记录**: 自动保存提取历史，方便回顾
- ⚙️ **个性化设置**: 可自定义提取选项和行为

## 支持的Amazon站点

- amazon.com (美国)
- amazon.cn (中国)
- amazon.co.uk (英国)
- amazon.de (德国)
- amazon.fr (法国)
- amazon.it (意大利)
- amazon.es (西班牙)
- amazon.ca (加拿大)
- amazon.com.au (澳大利亚)
- amazon.co.jp (日本)

## 安装方法

### 方法一：开发者模式安装（推荐）

1. 打开Chrome浏览器
2. 在地址栏输入 `chrome://extensions/`
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择 `AmazonProductExtractor` 文件夹
6. 插件安装完成！

### 方法二：打包安装

1. 在Chrome扩展程序页面点击"打包扩展程序"
2. 选择 `AmazonProductExtractor` 文件夹
3. 生成 `.crx` 文件
4. 拖拽 `.crx` 文件到Chrome扩展程序页面进行安装

## 使用方法

### 基本使用

1. 打开任意Amazon产品页面
2. 点击浏览器工具栏中的插件图标
3. 点击"提取产品信息"按钮
4. 查看提取的产品详细信息
5. 可选择导出为Excel或JSON格式

### 高级功能

#### 自动提取
- 在设置中启用"自动检测Amazon产品页面"
- 插件会在检测到产品页面时自动开始提取

#### 历史记录
- 插件会自动保存提取的产品信息
- 点击"历史记录"查看最近的提取记录
- 可在设置中关闭历史记录功能

#### 导出功能
- **Excel导出**: 生成CSV格式文件，包含所有产品信息
- **JSON导出**: 生成结构化JSON文件，便于程序处理

## 提取的数据字段

| 字段名 | 描述 | 示例 |
|--------|------|------|
| 产品名称 | 完整的产品标题 | "Apple iPhone 14 Pro Max" |
| ASIN码 | Amazon标准识别号 | "B0BDJQ9X8F" |
| 当前价格 | 产品当前售价 | "$999.00" |
| 原价 | 产品原始价格 | "$1099.00" |
| 评星 | 产品评分 | "4.5" |
| 评论数 | 评论总数 | "1,234" |
| 品牌 | 产品品牌 | "Apple" |
| 可用性 | 库存状态 | "In Stock" |
| 产品图片 | 高清图片链接 | "https://..." |
| 产品链接 | 当前页面URL | "https://..." |

## 文件结构

```
AmazonProductExtractor/
├── manifest.json      # 插件配置文件
├── popup.html         # 弹窗界面
├── popup.css          # 弹窗样式
├── popup.js           # 弹窗逻辑
├── content.js         # 内容脚本（页面数据提取）
├── background.js      # 后台脚本
└── README.md          # 说明文档
```

## 技术特点

- **Manifest V3**: 使用最新的Chrome扩展API
- **模块化设计**: 清晰的代码结构，易于维护
- **错误处理**: 完善的异常处理机制
- **数据清洗**: 智能的数据清理和格式化
- **多选择器**: 使用多个CSS选择器确保数据提取的可靠性

## 故障排除

### 常见问题

1. **插件无法提取数据**
   - 确保在Amazon产品详情页面使用
   - 检查页面是否完全加载
   - 尝试刷新页面后重新提取

2. **导出功能不工作**
   - 检查浏览器下载权限
   - 确保已成功提取产品数据

3. **插件图标不显示**
   - 检查插件是否正确安装
   - 尝试重新加载插件

### 调试模式

1. 打开Chrome开发者工具 (F12)
2. 切换到Console标签
3. 查看插件相关的日志信息

## 更新日志

### v1.0 (当前版本)
- 初始版本发布
- 支持基本的产品信息提取
- 支持Excel和JSON导出
- 包含历史记录功能
- 支持多个Amazon站点

## 注意事项

- 本插件仅用于学习和研究目的
- 请遵守Amazon的使用条款和robots.txt规定
- 不要过度频繁地使用，避免对服务器造成负担
- 提取的数据准确性可能因页面结构变化而受影响

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目！
