#!/usr/bin/env python3
"""
Chrome扩展程序打包脚本
生成可以直接安装的扩展程序包
"""

import os
import zipfile
import json
import shutil
from pathlib import Path

def validate_manifest():
    """验证manifest.json文件"""
    try:
        with open('manifest.json', 'r', encoding='utf-8') as f:
            manifest = json.load(f)
        print("✓ manifest.json 验证通过")
        return True
    except json.JSONDecodeError as e:
        print(f"✗ manifest.json 格式错误: {e}")
        return False
    except FileNotFoundError:
        print("✗ 找不到 manifest.json 文件")
        return False

def create_extension_package():
    """创建扩展程序包"""
    
    # 验证manifest文件
    if not validate_manifest():
        return False
    
    # 需要包含的文件
    files_to_include = [
        'manifest.json',
        'popup.html',
        'popup.css', 
        'popup.js',
        'content.js',
        'background.js'
    ]
    
    # 检查所有必需文件是否存在
    missing_files = []
    for file in files_to_include:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"✗ 缺少必需文件: {', '.join(missing_files)}")
        return False
    
    # 创建zip包
    package_name = 'amazon_product_extractor.zip'
    
    try:
        with zipfile.ZipFile(package_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for file in files_to_include:
                zipf.write(file)
                print(f"✓ 已添加: {file}")
        
        print(f"\n✓ 扩展程序包创建成功: {package_name}")
        print(f"文件大小: {os.path.getsize(package_name)} 字节")
        
        # 创建安装说明
        create_install_instructions()
        
        return True
        
    except Exception as e:
        print(f"✗ 创建包时出错: {e}")
        return False

def create_install_instructions():
    """创建安装说明文件"""
    instructions = """
# Amazon Product Extractor 安装说明

## 方法1: 开发者模式安装 (推荐)

1. 打开Chrome浏览器
2. 在地址栏输入: chrome://extensions/
3. 打开右上角的"开发者模式"开关
4. 点击"加载已解压的扩展程序"
5. 选择解压后的扩展程序文件夹
6. 扩展程序将自动加载并可以使用

## 方法2: 使用zip包

1. 将 amazon_product_extractor.zip 解压到一个文件夹
2. 按照方法1的步骤进行安装

## 使用说明

1. 访问任意Amazon产品页面
2. 点击浏览器工具栏中的扩展程序图标
3. 点击"提取产品信息"按钮
4. 查看提取的产品信息
5. 可以导出为Excel或JSON格式

## 支持的Amazon站点

- amazon.com (美国)
- amazon.cn (中国)  
- amazon.co.uk (英国)
- amazon.de (德国)
- amazon.fr (法国)
- amazon.it (意大利)
- amazon.es (西班牙)
- amazon.ca (加拿大)
- amazon.com.au (澳大利亚)
- amazon.co.jp (日本)

## 故障排除

如果扩展程序无法正常工作:
1. 确保在Amazon产品页面使用
2. 检查是否启用了扩展程序
3. 刷新页面后重试
4. 检查浏览器控制台是否有错误信息

版本: 1.0
"""
    
    with open('安装说明.txt', 'w', encoding='utf-8') as f:
        f.write(instructions)
    
    print("✓ 已创建安装说明文件: 安装说明.txt")

if __name__ == "__main__":
    print("Amazon Product Extractor 打包工具")
    print("=" * 40)
    
    if create_extension_package():
        print("\n🎉 打包完成!")
        print("\n下一步:")
        print("1. 解压 amazon_product_extractor.zip")
        print("2. 在Chrome中访问 chrome://extensions/")
        print("3. 开启开发者模式")
        print("4. 点击'加载已解压的扩展程序'")
        print("5. 选择解压后的文件夹")
    else:
        print("\n❌ 打包失败，请检查错误信息")
